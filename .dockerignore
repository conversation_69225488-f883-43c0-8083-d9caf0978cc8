# Dependencies
node_modules
npm-debug.log*

# Build outputs
dist
build

# Environment files
.env
.env.local
.env.*.local

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# Logs
logs
*.log

# Coverage
coverage

# Temporary files
*.tmp
*.temp

# Documentation
README*.md
docs/

# Test files
test/
*.test.ts
*.spec.ts

# Development files
.eslintrc.js
.prettierrc
jest.config.js
