import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobsService } from './jobs.service';
import { JobsController } from './jobs.controller';
import { JobPost } from './entities/job.entity';
import { SavedJob } from './entities/saved-job.entity';

@Module({
  imports: [TypeOrmModule.forFeature([JobPost, SavedJob])],
  controllers: [JobsController],
  providers: [JobsService],
  exports: [JobsService, TypeOrmModule],
})
export class JobsModule {}
