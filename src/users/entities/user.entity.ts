import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { UserRole } from '../../common/enums/user-role.enum';
import { Exclude } from 'class-transformer';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn()
  @Column({ name: 'user_id' })
  user_id: number;

  @Column({ name: 'username', length: 50, unique: true })
  username: string;

  @Column({ name: 'password', length: 255 })
  @Exclude()
  password: string;

  @Column({ name: 'full_name', length: 100, nullable: true })
  full_name?: string;

  @Column({ name: 'email', length: 100, unique: true, nullable: true })
  email?: string;

  @Column({ name: 'phone', length: 20, nullable: true })
  phone?: string;

  @Column({ name: 'address', type: 'text', nullable: true })
  address?: string;

  @Column({ name: 'avatar', length: 255, nullable: true })
  avatar?: string;

  @Column({
    name: 'role',
    type: 'enum',
    enum: UserRole,
    default: UserRole.JOB_SEEKER,
  })
  role: UserRole;

  @Column({ name: 'refresh_token', nullable: true })
  @Exclude()
  refresh_token?: string;

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updated_at: Date;

  // Relations will be added later
  // @OneToMany(() => Resume, (resume) => resume.user)
  // resumes: Resume[];

  // @OneToMany(() => Application, (application) => application.user)
  // applications: Application[];

  // @OneToMany(() => SavedJob, (savedJob) => savedJob.user)
  // saved_jobs: SavedJob[];

  // @OneToMany(() => FollowedCompany, (followedCompany) => followedCompany.user)
  // followed_companies: FollowedCompany[];

  // @OneToMany(() => Notification, (notification) => notification.recipient)
  // notifications: Notification[];
}
