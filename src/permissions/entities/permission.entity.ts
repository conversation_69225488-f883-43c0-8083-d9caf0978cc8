import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, <PERSON>umn, OneToMany } from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('permissions')
export class Permission {
  @PrimaryGeneratedColumn()
  permission_id: number;

  @Column({ name: 'permission_name', length: 100, unique: true })
  permission_name: string;

  @OneToMany(() => User, (user) => user.permission)
  users: User[];
}
