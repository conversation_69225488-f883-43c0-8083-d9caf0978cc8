import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './database/database.module';
import { UsersModule } from './users/users.module';
import { CompaniesModule } from './companies/companies.module';
import { JobsModule } from './jobs/jobs.module';
import { ApplicationsModule } from './applications/applications.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    CompaniesModule,
    JobsModule,
    ApplicationsModule,
    // DatabaseModule, // Temporarily disabled for testing
    // UsersModule,   // Temporarily disabled for testing
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
