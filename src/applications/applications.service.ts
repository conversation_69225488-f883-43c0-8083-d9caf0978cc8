import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Application } from './entities/application.entity';
import { JobPost } from '../jobs/entities/job.entity';
import { Resume } from '../resumes/entities/resume.entity';
import { CreateApplicationDto } from './dto/create-application.dto';
import { UpdateApplicationDto } from './dto/update-application.dto';
import { ApplicationStatus } from '../common/enums/application-status.enum';

@Injectable()
export class ApplicationsService {
  constructor(
    @InjectRepository(Application)
    private readonly applicationRepository: Repository<Application>,
    @InjectRepository(JobPost)
    private readonly jobRepository: Repository<JobPost>,
    @InjectRepository(Resume)
    private readonly resumeRepository: Repository<Resume>,
  ) {}

  async create(
    createApplicationDto: CreateApplicationDto,
  ): Promise<Application> {
    // Verify job exists
    const job = await this.jobRepository.findOne({
      where: { job_id: createApplicationDto.job_id },
    });

    if (!job) {
      throw new BadRequestException(
        `Job with ID ${createApplicationDto.job_id} not found`,
      );
    }

    // Verify resume exists
    const resume = await this.resumeRepository.findOne({
      where: { resume_id: createApplicationDto.resume_id },
    });

    if (!resume) {
      throw new BadRequestException(
        `Resume with ID ${createApplicationDto.resume_id} not found`,
      );
    }

    // Check if user already applied for this job
    const existingApplication = await this.applicationRepository.findOne({
      where: {
        job_id: createApplicationDto.job_id,
        resume_id: createApplicationDto.resume_id,
      },
    });

    if (existingApplication) {
      throw new BadRequestException('You have already applied for this job');
    }

    const application = this.applicationRepository.create({
      ...createApplicationDto,
      status: ApplicationStatus.PENDING,
    });

    return await this.applicationRepository.save(application);
  }

  async findAll(
    filters: {
      page?: number;
      limit?: number;
      status?: ApplicationStatus;
      job_id?: number;
      user_id?: number;
    } = {},
  ): Promise<{
    applications: Application[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const { page = 1, limit = 10, status, job_id, user_id } = filters;

    const queryBuilder = this.applicationRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.job_post', 'job_post')
      .leftJoinAndSelect('application.resume', 'resume')
      .leftJoinAndSelect('resume.user', 'user')
      .leftJoinAndSelect('job_post.company', 'company');

    // Filter by status
    if (status) {
      queryBuilder.andWhere('application.status = :status', { status });
    }

    // Filter by job
    if (job_id) {
      queryBuilder.andWhere('application.job_id = :job_id', { job_id });
    }

    // Filter by user (through resume)
    if (user_id) {
      queryBuilder.andWhere('resume.user_id = :user_id', { user_id });
    }

    // Pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Order by application date
    queryBuilder.orderBy('application.applied_at', 'DESC');

    const [applications, total] = await queryBuilder.getManyAndCount();

    return {
      applications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: number): Promise<Application> {
    const application = await this.applicationRepository.findOne({
      where: { application_id: id },
      relations: [
        'job_post',
        'resume',
        'resume.user',
        'job_post.company',
        'interviews',
      ],
    });

    if (!application) {
      throw new NotFoundException(`Application with ID ${id} not found`);
    }

    return application;
  }

  async update(
    id: number,
    updateApplicationDto: UpdateApplicationDto,
  ): Promise<Application> {
    const application = await this.findOne(id);
    Object.assign(application, updateApplicationDto);
    return await this.applicationRepository.save(application);
  }

  async remove(id: number): Promise<void> {
    const application = await this.findOne(id);
    await this.applicationRepository.remove(application);
  }

  async getApplicationsByUser(userId: number): Promise<Application[]> {
    return await this.applicationRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.job_post', 'job_post')
      .leftJoinAndSelect('application.resume', 'resume')
      .leftJoinAndSelect('job_post.company', 'company')
      .where('resume.user_id = :userId', { userId })
      .orderBy('application.applied_at', 'DESC')
      .getMany();
  }

  async getApplicationsByJob(jobId: number): Promise<Application[]> {
    return await this.applicationRepository.find({
      where: { job_id: jobId },
      relations: ['resume', 'resume.user'],
      order: { applied_at: 'DESC' },
    });
  }
}
