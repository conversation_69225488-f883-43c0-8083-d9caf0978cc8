# Docker Development Environment Variables

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=workfinder_user
DB_PASSWORD=workfinder_password
DB_NAME=workfinder_db

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_for_development_make_it_long_and_secure_at_least_32_characters
JWT_EXPIRES_IN=1h
JWT_REFRESH_SECRET=your_refresh_secret_key_for_development_also_long_and_secure_at_least_32_characters
JWT_REFRESH_EXPIRES_IN=7d

# Application Configuration
NODE_ENV=development
PORT=3000
API_PREFIX=api/v1

# File Upload Configuration
UPLOAD_DEST=./uploads
MAX_FILE_SIZE=10485760

# Redis Configuration (for future caching)
REDIS_HOST=redis
REDIS_PORT=6379

# CORS Configuration
CORS_ORIGIN=http://localhost:3001,http://localhost:5173,http://localhost:4200

# Logging
LOG_LEVEL=debug
